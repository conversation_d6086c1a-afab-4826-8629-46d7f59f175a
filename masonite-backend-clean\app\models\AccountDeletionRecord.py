""" AccountDeletionRecord Model """

from masoniteorm.models import Model
from datetime import datetime, timedelta
import secrets
import uuid


class AccountDeletionRecord(Model):
    """AccountDeletionRecord Model for GDPR-compliant account deletion"""

    __fillable__ = [
        'user_id', 'email', 'deletion_id', 'deletion_status', 'confirmation_token',
        'confirmation_token_expires', 'preserve_payment_data', 'preserve_transaction_history',
        'preserve_profile_data', 'preserve_security_logs', 'custom_retention_period',
        'reason', 'preserved_user_data', 'preserved_payment_data', 'preserved_transaction_data',
        'preserved_security_data', 'requested_at', 'confirmed_at', 'completed_at', 'expires_at'
    ]

    __casts__ = {
        'preserve_payment_data': 'boolean',
        'preserve_transaction_history': 'boolean',
        'preserve_profile_data': 'boolean',
        'preserve_security_logs': 'boolean',
        'custom_retention_period': 'integer',
        'preserved_user_data': 'json',
        'preserved_payment_data': 'json',
        'preserved_transaction_data': 'json',
        'preserved_security_data': 'json',
        'requested_at': 'datetime',
        'confirmed_at': 'datetime',
        'completed_at': 'datetime',
        'expires_at': 'datetime',
        'confirmation_token_expires': 'datetime'
    }

    @classmethod
    def create_deletion_request(cls, user_id, email, preferences):
        """Create a new account deletion request"""
        deletion_id = str(uuid.uuid4())
        confirmation_token = secrets.token_urlsafe(32)

        return cls.create({
            'user_id': str(user_id),
            'email': email,
            'deletion_id': deletion_id,
            'deletion_status': 'pending_confirmation',
            'confirmation_token': confirmation_token,
            'confirmation_token_expires': datetime.now() + timedelta(hours=24),
            'preserve_payment_data': preferences.get('preservePaymentData', False),
            'preserve_transaction_history': preferences.get('preserveTransactionHistory', False),
            'preserve_profile_data': preferences.get('preserveProfileData', False),
            'preserve_security_logs': preferences.get('preserveSecurityLogs', False),
            'custom_retention_period': preferences.get('customRetentionPeriod', 30),
            'reason': preferences.get('reason'),
            'requested_at': datetime.now(),
            'expires_at': datetime.now() + timedelta(days=preferences.get('customRetentionPeriod', 30))
        })

    def is_confirmation_token_valid(self):
        """Check if confirmation token is still valid"""
        if not self.confirmation_token_expires:
            return False

        # Handle both datetime objects and strings
        try:
            if isinstance(self.confirmation_token_expires, str):
                from datetime import datetime as dt
                expires_dt = dt.fromisoformat(self.confirmation_token_expires.replace('Z', '+00:00'))
            else:
                expires_dt = self.confirmation_token_expires

            return datetime.now() < expires_dt
        except Exception as e:
            print(f"❌ Error validating confirmation token: {str(e)}")
            return False

    def confirm_deletion(self):
        """Confirm the deletion request"""
        try:
            self.deletion_status = 'confirmed'
            self.confirmed_at = datetime.now()
            self.save()
            print(f"✅ Deletion confirmed for record: {self.id}")
        except Exception as e:
            print(f"❌ Error confirming deletion: {str(e)}")
            raise e

    def complete_deletion(self):
        """Mark deletion as completed"""
        try:
            self.deletion_status = 'completed'
            self.completed_at = datetime.now()
            self.save()
            print(f"✅ Deletion completed for record: {self.id}")
        except Exception as e:
            print(f"❌ Error completing deletion: {str(e)}")
            raise e

    def cancel_deletion(self):
        """Cancel the deletion request"""
        self.deletion_status = 'cancelled'
        self.save()

    def is_expired(self):
        """Check if the deletion record has expired"""
        if not self.expires_at:
            return False

        # Handle both datetime objects and strings
        try:
            if isinstance(self.expires_at, str):
                from datetime import datetime as dt
                expires_dt = dt.fromisoformat(self.expires_at.replace('Z', '+00:00'))
            else:
                expires_dt = self.expires_at

            return datetime.now() > expires_dt
        except Exception as e:
            print(f"❌ Error checking expiration: {str(e)}")
            return False

    @classmethod
    def find_by_email(cls, email):
        """Find deletion record by email"""
        return cls.where('email', email).where('deletion_status', '!=', 'cancelled').first()

    @classmethod
    def find_by_token(cls, token):
        """Find deletion record by confirmation token"""
        return cls.where('confirmation_token', token).first()

    @classmethod
    def cleanup_expired(cls):
        """Clean up expired deletion records"""
        expired_records = cls.where('expires_at', '<', datetime.now()).get()
        count = len(expired_records)

        for record in expired_records:
            record.delete()

        return count
